/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: [],
    formats: ['image/webp', 'image/avif'],
  },
  async rewrites() {
    return [
      // Ensure static files in /category/ are served correctly
      {
        source: '/category/:path*.png',
        destination: '/category/:path*.png',
      },
      {
        source: '/category/:path*.jpg',
        destination: '/category/:path*.jpg',
      },
      {
        source: '/category/:path*.jpeg',
        destination: '/category/:path*.jpeg',
      },
      {
        source: '/category/:path*.svg',
        destination: '/category/:path*.svg',
      },
    ];
  },
};

module.exports = nextConfig;
